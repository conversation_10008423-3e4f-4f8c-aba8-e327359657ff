## Lease Database Tables - Current Relational Structure

### Core Document Tables

__1. documents__ (Document Storage)

- id (uuid, PRIMARY KEY)
- name (text)
- created_at (timestamptz)
- complete_content (text)
- document_language (enum: 'English', 'Français')
- document_date (date)
- document_type (text)
DONE

__2. document_sections__ (Document Sections)

- id (uuid, PRIMARY KEY)
- document_id (uuid, FOREIGN KEY → documents.id)
- content (text)
- doc_order (integer)
- created_at (timestamptz)
DONE

### Lease Document Tables

__3. lease_documents__

- lease_id (uuid, PRIMARY KEY)
- lease_term_duration (text)
- lease_term_years (integer)
- lease_term_months (integer)
- commencement_date (date)
- end_date (date)
- possession_date (date)
- installation_period_months (integer)
- installation_period_start (date)
- installation_period_end (date)
- lease_status (text)
- created_date (timestamptz)
- modified_date (timestamptz)
- version_number (integer)
- document_id (uuid, FOREIGN KEY → documents.id)
- lease_execution_date (date)
DONE

__4. parties__

- party_id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- party_type (text)
- party_name (text)
- legal_status (text)
- address_street (text)
- address_city (text)
- address_province (text)
- address_postal_code (text)
- representative_name (text)
- representative_title (text)
- authorization_details (text)
- email (text)
- phone_number (text)
DONE

__5. properties__

- property_id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- leased_premises_address (text)
- building_address (text)
- land_lot_number (text)
- cadastre_details (text)
- rental_area_sqft (numeric)
- measurement_method (text)
- property_condition (text)
- plan_reference (text)
- physical_boundaries_description (text)
DONE

__6. financial_terms__

- financial_id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- annual_rent_per_sqft (numeric)
- annual_base_rent (numeric)
- monthly_base_rent (numeric)
- rent_currency (text)
- rent_payment_frequency (text)
- rent_due_day (integer)
- rent_payment_terms (text)
- gst_rate (numeric)
- qst_rate (numeric)
- late_payment_fee (numeric)
- late_payment_threshold (integer)
- interest_rate_formula (text)
- assignment_admin_fee (numeric)
- authorized_transfer_admin_fee (numeric)
- utilities_responsibility (text)
- admin_fee_on_operating_expenses_rate (numeric)
- admin_fee_on_taxes_rate (numeric)
- local_improvement_tax_terms (text)
- tax_payment_method (text)
- tenant_business_tax_responsibility_details (text)
- utility_cost_recovery_mechanisms (text)
DONE

__7. security_deposits__

- deposit_id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- security_deposit_base (numeric)
- security_deposit_taxes (numeric)
- security_deposit_total (numeric)
- security_deposit_payment_method (text)
- security_deposit_interest_accrual_terms (text)
- security_deposit_conditions (text)
DONE

### New Lease Financial & Terms Tables

__8. rent_escalations__ (MULTIPLE)

- id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- escalation_type (text)
- start_date (date)
- end_date (date)
- amount_or_formula (text)
- frequency (text)
- review_mechanism (text)
- annual_base_rent_for_period (numeric)
- monthly_base_rent_for_period (numeric)
- rate_per_sqft_for_period (numeric)
- created_at (timestamptz)
- updated_at (timestamptz)
DONE

__9. operating_costs_details__ (MULTIPLE)

- id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- cost_type (text)
- calculation_method (text)
- inclusions (text)
- exclusions (text)
- cap_type (text)
- cap_amount_or_formula (numeric)
- reconciliation_frequency (text)
- recovery_method (text)
- gross_up_provision_enabled (boolean)
- gross_up_occupancy_percentage (numeric)
- created_at (timestamptz)
- updated_at (timestamptz)
DONE

__10. guarantors__ (MULTIPLE)

- id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- party_id (uuid, FOREIGN KEY → parties.party_id)
- guarantee_type (text)
- max_liability_amount (numeric)
- guarantee_duration_start (date)
- guarantee_duration_end (date)
- conditions (text)
- created_at (timestamptz)
- updated_at (timestamptz)
DONE

__11. early_termination_options__ (MULTIPLE)

- id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- party_id (uuid, FOREIGN KEY → parties.party_id)
- termination_condition (text)
- notice_period_days (integer)
- penalty_amount_or_formula (text)
- effective_date_clause (text)
- conditions_for_exercise (text)
- created_at (timestamptz)
- updated_at (timestamptz)
DONE

__12. premises_condition_terms__ (MULTIPLE)

- id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- condition_type (text)
- description (text)
- landlord_obligations (text)
- tenant_obligations (text)
- make_good_requirements (text)
- delivery_condition_description (text)
- created_at (timestamptz)
- updated_at (timestamptz)
DONE

__13. dispute_resolution__ (MULTIPLE)

- id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- method_type (text)
- governing_law (text)
- jurisdiction (text)
- arbitration_rules (text)
- mediation_requirements (text)
- notice_period_before_action (integer)
- created_at (timestamptz)
- updated_at (timestamptz)
DONE

__14. leased_amenities__

- id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- amenity_type (text)
- quantity (integer)
- location_description (text)
- cost_per_unit (numeric)
- exclusivity (boolean)
- cost_per_unit_frequency (text)
- cost_escalation_terms (text)
- termination_terms (text)
- created_at (timestamptz)
- updated_at (timestamptz)
DONE

__15. renewal_options__ (MULTIPLE)

- id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- renewal_rent_escalation_id (uuid, FOREIGN KEY → rent_escalations.id)
- option_number (integer)
- renewal_term_duration (text)
- rent_calculation_method (text)
- notice_period_days (integer)
- conditions_for_exercise (text)
- rent_review_mechanism_details (text)
- conditions_precedent_details (text)
- personal_right_clause (boolean)
- excluded_inducements_on_renewal (text)
- created_at (timestamptz)
- updated_at (timestamptz)
DONE

__16. purchase_options__ (MULTIPLE)

- id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- option_type (text)
- conditions (text)
- purchase_price_formula (text)
- exercise_period (text)
- notice_requirements (text)
- created_at (timestamptz)
- updated_at (timestamptz)
DONE

__17. environmental_provisions__ *(MULTIPLE)*

- id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- provision_type (text)
- description (text)
- responsible_party (text)
- indemnification_terms (text)
- created_at (timestamptz)
- updated_at (timestamptz)

__18. tenant_inducements__ *(MULTIPLE)*

- id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- inducement_type (text)
- amount (numeric)
- conditions (text)
- payment_schedule (text)
- rent_free_period_start (date)
- rent_free_period_end (date)
- created_at (timestamptz)
- updated_at (timestamptz)

__19. brokers__ *(MULTIPLE)*

- id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- broker_name (text)
- company_name (text)
- broker_type (text)
- commission_details (text)
- created_at (timestamptz)
- updated_at (timestamptz)

__20. representations_warranties__ *(MULTIPLE)*

- id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- party_making_representation (text)
- description (text)
- indemnification_terms (text)
- created_at (timestamptz)
- updated_at (timestamptz)

### NEW Advanced Lease Provision Tables

__21. property_managers__ *(NEW)*

- id (uuid, PRIMARY KEY)
- company_name (varchar)
- contact_person (varchar)
- email (varchar)
- phone_primary (varchar)
- address_street (varchar)
- address_city (varchar)
- address_province (varchar)
- address_postal_code (varchar)
- notes (text)
- created_at (timestamptz)
- updated_at (timestamptz)

__22. lenders__ *(NEW - MULTIPLE)*

- id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- lender_name (varchar)
- contact_person (varchar)
- loan_type (text)
- notes (text)
- created_at (timestamptz)
- updated_at (timestamptz)

__23. relocation_clauses__ *(NEW - MULTIPLE)*

- id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- notice_period_days (integer)
- landlord_cost_responsibility_details (text)
- tenant_compensation_exclusions (text)
- conditions (text)
- created_at (timestamptz)
- updated_at (timestamptz)

__24. condition_precedent_clauses__ *(NEW - MULTIPLE)*

- id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- condition_description (text)
- trigger_date (date)
- consequence_of_failure (text)
- party_responsible_for_fulfillment (text)
- created_at (timestamptz)
- updated_at (timestamptz)

__25. indemnification_clauses__ *(NEW - MULTIPLE)*

- id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- indemnifying_party (text)
- indemnified_party (text)
- scope_of_indemnity (text)
- triggering_events (text)
- notes (text)
- created_at (timestamptz)
- updated_at (timestamptz)

__26. estoppel_certificate_provisions__ *(NEW - MULTIPLE)*

- id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- response_time_days (integer)
- required_information_details (text)
- party_requesting (text)
- party_providing (text)
- created_at (timestamptz)
- updated_at (timestamptz)
DONE

__27. subordination_attornment_provisions__ *(NEW - MULTIPLE)*

- id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- subordination_type (text)
- attornment_requirement (boolean)
- document_delivery_time_days (integer)
- notes (text)
- created_at (timestamptz)
- updated_at (timestamptz)

__28. power_of_attorney_provisions__ *(NEW - MULTIPLE)*

- id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- appointed_party (text)
- granting_party (text)
- scope_of_authority (text)
- trigger_condition (text)
- created_at (timestamptz)
- updated_at (timestamptz)

__29. landlord_release_provisions__ *(NEW - MULTIPLE)*

- id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- release_event (text)
- release_details (text)
- created_at (timestamptz)
- updated_at (timestamptz)

__30. financial_information_provisions__ *(NEW - MULTIPLE)*

- id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- party_required_to_provide (text)
- information_type (text)
- requesting_parties (text)
- notes (text)
- created_at (timestamptz)
- updated_at (timestamptz)

### Lease Terms & Restrictions Tables

__31. use_restrictions__ (MULTIPLE)

- restriction_id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- restriction_type (text)
- restriction_description (text)
- restriction_category (text)

__32. maintenance_obligations__ *(UPDATED - MULTIPLE)*

- maintenance_id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- responsible_party (text)
- maintenance_type (text)
- maintenance_description (text)
- notification_requirements (text)
- cost_responsibility (text)
- **admin_fee_on_landlord_costs_rate (numeric)** *(NEW)*
- **scope_details (text)** *(NEW)*

__33. improvement_terms__ *(UPDATED - MULTIPLE)*

- improvement_id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- improvement_type (text)
- cost_responsibility (text)
- admin_fee_percentage (numeric)
- quality_requirements (text)
- approval_required (boolean)
- contractor_restrictions (text)
- ownership_terms (text)
- construction_timeline_days (integer)
- permit_requirements (text)
- lien_waiver_requirements (text)
- **restriction_description (text)** *(NEW)*
- **removal_requirements_details (text)** *(NEW)*

__34. access_inspection_rights__ (MULTIPLE)

- access_id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- access_type (text)
- notice_requirements (text)
- access_rights_description (text)
- time_restrictions (text)

__35. compliance_requirements__ (MULTIPLE)

- compliance_id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- compliance_type (text)
- compliance_description (text)
- responsibility_party (text)
- penalty_terms (text)

__36. signage_provisions__ *(UPDATED - MULTIPLE)*

- signage_id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- signage_type (text)
- approval_required (boolean)
- signage_restrictions (text)
- cost_responsibility (text)
- removal_requirements (text)
- **consent_conditions (text)** *(NEW)*

__37. assignment_subletting_terms__ *(UPDATED - MULTIPLE)*

- assignment_id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- transfer_type (text)
- consent_required (boolean)
- refusal_reasons (text)
- response_time_days (integer)
- information_requirements (text)
- admin_fee (numeric)
- landlord_recapture_right (boolean)
- **landlord_consent_factors (text)** *(NEW)*
- **recapture_excess_consideration_details (text)** *(NEW)*
- **recapture_excess_rent_details (text)** *(NEW)*
- **landlord_recapture_response_time_days (integer)** *(NEW)*
- **tenant_withdrawal_period_days (integer)** *(NEW)*
- **solidary_liability_clause (boolean)** *(NEW)*
- **subtenant_direct_payment_to_landlord (boolean)** *(NEW)*
- **permitted_transfer_conditions (text)** *(NEW)*

__38. default_remedies__ *(UPDATED - MULTIPLE)*

- default_id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- default_type (text)
- cure_period_days (integer)
- default_description (text)
- remedy_description (text)
- accelerated_rent_months (integer)
- interest_calculation (text)
- is_monetary_default (boolean)
- is_non_monetary_default (boolean)
- specific_non_monetary_default_type (text)
- **admin_fee_on_remedy_costs_rate (numeric)** *(NEW)*
- **specific_consequences_details (text)** *(NEW)*

__39. insurance_liability_terms__ *(UPDATED - MULTIPLE)*

- insurance_id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- provision_type (text)
- responsible_party (text)
- provision_description (text)
- exclusions (text)
- minimum_coverage_amount (numeric)
- named_insureds (text)
- certificate_of_insurance_requirement (boolean)
- deductible_limit (numeric)
- **insurance_types_required_details (text)** *(NEW)*
- **waiver_of_subrogation_clause (boolean)** *(NEW)*
- **insurer_notification_period_days (integer)** *(NEW)*
- **consequences_of_non_compliance (text)** *(NEW)*
- **landlord_insurance_exclusions (text)** *(NEW)*

__40. force_majeure_provisions__ (MULTIPLE)

- force_majeure_id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- event_type (text)
- event_description (text)
- effect_on_obligations (text)
- rent_payment_exemption (boolean)
DONE

__41. destruction_expropriation_terms__ *(UPDATED - MULTIPLE)*

- destruction_id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- event_type (text)
- repair_timeline_days (integer)
- termination_rights (text)
- rent_abatement_provisions (text)
- cost_responsibility (text)
- compensation_allocation_landlord (text)
- compensation_allocation_tenant (text)
- **tenant_termination_notice_period_days (integer)** *(NEW)*
- **landlord_termination_notice_period_days (integer)** *(NEW)*
- **building_destruction_percentage_trigger (numeric)** *(NEW)*
- **landlord_termination_building_destruction_notice_period_days (integer)** *(NEW)*
- **restoration_obligations_details (text)** *(NEW)*
- **tenant_work_after_destruction (text)** *(NEW)*
- **no_allowance_after_destruction_clause (boolean)** *(NEW)*

__42. expiration_holdover_terms__

- expiration_id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- tacit_renewal_excluded (boolean)
- holdover_rent_percentage (numeric)
- holdover_terms (text)
- property_return_condition (text)
- removal_obligations (text)

__43. notice_provisions__ *(MULTIPLE)*

- notice_id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- party_type (text)
- notice_address (text)
- delivery_methods (text)
- service_domicile (text)
- contact_person_name (text)
- contact_person_title (text)

__44. legal_general_provisions__ *(MULTIPLE)*

- legal_id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- provision_type (text)
- provision_description (text)
- jurisdiction (text)
- requirements (text)
- is_confidentiality_clause (boolean)
- scope_of_confidentiality (text)

__45. health_emergency_provisions__ (MULTIPLE)

- health_emergency_id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- provision_type (text)
- provision_description (text)
- landlord_rights (text)
- liability_exclusions (text)

__46. signatures__ (MULTIPLE)

- signature_id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- party_type (text)
- signatory_name (text)
- signatory_title (text)
- execution_date (date)

__47. document_attachments__ (MULTIPLE)

- attachment_id (uuid, PRIMARY KEY)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- attachment_type (text)
- attachment_name (text)
- attachment_description (text)
- annex_reference (text)
DONE

### Property Management Tables

__48. property_portfolio__ (Main Property Table)

- id (uuid, PRIMARY KEY)
- property_name (varchar)
- property_type (varchar)
- address_street (varchar)
- address_city (varchar)
- address_province (varchar)
- address_postal_code (varchar)
- address_country (varchar, default: 'Canada')
- total_area_sqft (numeric)
- buildable_area_sqft (numeric)
- year_built (integer)
- number_of_units (integer)
- number_of_floors (integer)
- parking_spaces (integer)
- property_status (varchar, default: 'active')
- acquisition_date (date)
- acquisition_price (numeric)
- current_market_value (numeric)
- property_manager_id (uuid)
- property_description (text)
- amenities (jsonb)
- zoning_classification (varchar)
- property_tax_assessment (numeric)
- insurance_policy_number (varchar)
- created_at (timestamptz)
- updated_at (timestamptz)
- property_external_id (varchar)
- property_url (text)
- contact_name (varchar)
- contact_phone (varchar)
- contact_email (varchar)
- media_urls (jsonb, default: '{}')
- available_spaces (jsonb, default: '[]')
- nearby_properties (jsonb, default: '[]')

__49. property_units__

- id (uuid, PRIMARY KEY)
- property_id (uuid, FOREIGN KEY → property_portfolio.id)
- unit_number (varchar)
- unit_type (varchar)
- floor_number (integer)
- square_footage (numeric)
- bedrooms (integer)
- bathrooms (numeric)
- unit_status (varchar, default: 'vacant')
- market_rent (numeric)
- current_rent (numeric)
- rent_controlled (boolean, default: false)
- unit_features (jsonb)
- appliances_included (jsonb)
- last_renovation_date (date)
- move_in_ready (boolean, default: true)
- accessibility_features (jsonb)
- unit_notes (text)
- created_at (timestamptz)
- updated_at (timestamptz)

__50. property_features__

- id (uuid, PRIMARY KEY)
- property_id (uuid, FOREIGN KEY → property_portfolio.id)
- feature_type (varchar)
- feature_name (varchar)
- feature_description (text)
- is_available (boolean, default: true)
- maintenance_required (boolean, default: false)
- last_service_date (date)
- next_service_date (date)
- created_at (timestamptz)
- updated_at (timestamptz)

__51. property_valuations__

- id (uuid, PRIMARY KEY)
- property_id (uuid, FOREIGN KEY → property_portfolio.id)
- valuation_date (date)
- valuation_amount (numeric)
- valuation_type (varchar)
- appraiser_name (varchar)
- appraiser_company (varchar)
- appraiser_license (varchar)
- valuation_method (varchar)
- square_foot_value (numeric)
- valuation_notes (text)
- report_url (varchar)
- created_at (timestamptz)

__52. property_inspections__

- id (uuid, PRIMARY KEY)
- property_id (uuid, FOREIGN KEY → property_portfolio.id)
- lease_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- inspection_type (varchar)
- inspection_date (date)
- inspector_name (varchar)
- inspector_company (varchar)
- inspection_status (varchar, default: 'scheduled')
- overall_condition (varchar)
- inspection_notes (text)
- issues_found (jsonb)
- recommendations (text)
- follow_up_required (boolean, default: false)
- follow_up_date (date)
- inspection_report_url (varchar)
- photos (jsonb)
- tenant_present (boolean, default: false)
- tenant_signature (varchar)
- inspector_signature (varchar)
- created_at (timestamptz)
- updated_at (timestamptz)

__53. property_expenses__

- id (uuid, PRIMARY KEY)
- property_id (uuid, FOREIGN KEY → property_portfolio.id)
- expense_type (varchar)
- expense_category (varchar)
- description (text)
- amount (numeric)
- currency (varchar, default: 'CAD')
- expense_date (date)
- vendor_name (varchar)
- vendor_contact (varchar)
- invoice_number (varchar)
- payment_method (varchar)
- payment_status (varchar, default: 'pending')
- payment_date (date)
- is_recurring (boolean, default: false)
- recurring_frequency (varchar)
- next_due_date (date)
- receipt_url (varchar)
- notes (text)
- created_by (uuid)
- created_at (timestamptz)
- updated_at (timestamptz)

### Tenant Management Tables

__54. tenants__

- id (uuid, PRIMARY KEY)
- tenant_name (varchar)
- tenant_type (varchar)
- business_name (varchar)
- contact_person (varchar)
- email (varchar)
- phone_primary (varchar)
- phone_secondary (varchar)
- address_street (varchar)
- address_city (varchar)
- address_province (varchar)
- address_postal_code (varchar)
- address_country (varchar, default: 'Canada')
- emergency_contact_name (varchar)
- emergency_contact_phone (varchar)
- emergency_contact_relationship (varchar)
- tenant_status (varchar, default: 'active')
- credit_score (integer)
- annual_income (numeric)
- employment_status (varchar)
- employer_name (varchar)
- tenant_notes (text)
- move_in_date (date)
- move_out_date (date)
- created_at (timestamptz)
- updated_at (timestamptz)

__55. rental_applications__

- id (uuid, PRIMARY KEY)
- property_id (uuid, FOREIGN KEY → property_portfolio.id)
- unit_id (uuid, FOREIGN KEY → property_units.id)
- applicant_name (varchar)
- applicant_email (varchar)
- applicant_phone (varchar)
- current_address (text)
- desired_move_in_date (date)
- application_status (varchar, default: 'pending')
- employment_status (varchar)
- employer_name (varchar)
- annual_income (numeric)
- credit_score (integer)
- personal_references (jsonb)
- pets (jsonb)
- additional_occupants (jsonb)
- application_date (date, default: CURRENT_DATE)
- screening_completed (boolean, default: false)
- background_check_status (varchar)
- credit_check_status (varchar)
- reference_check_status (varchar)
- application_fee_paid (boolean, default: false)
- application_fee_amount (numeric)
- notes (text)
- decision_date (date)
- decision_reason (text)
- created_at (timestamptz)
- updated_at (timestamptz)

__56. tenant_communications__

- id (uuid, PRIMARY KEY)
- tenant_id (uuid, FOREIGN KEY → tenants.id)
- property_id (uuid, FOREIGN KEY → property_portfolio.id)
- communication_type (varchar)
- subject (varchar)
- content (text)
- communication_date (timestamptz, default: now())
- communication_method (varchar)
- status (varchar, default: 'sent')
- priority (varchar, default: 'normal')
- follow_up_required (boolean, default: false)
- follow_up_date (date)
- attachments (jsonb)
- related_maintenance_request (uuid)
- staff_member (uuid)
- created_at (timestamptz)
- updated_at (timestamptz)

### Lease Agreement Tables

__57. lease_agreements__ (Simplified Lease Structure)

- id (uuid, PRIMARY KEY)
- lease_document_id (uuid, FOREIGN KEY → lease_documents.lease_id)
- property_id (uuid, FOREIGN KEY → property_portfolio.id)
- tenant_id (uuid, FOREIGN KEY → tenants.id)
- unit_number (varchar)
- lease_type (varchar)
- lease_status (varchar, default: 'active')
- start_date (date)
- end_date (date)
- monthly_rent (numeric)
- security_deposit (numeric)
- pet_deposit (numeric)
- last_rent_increase_date (date)
- next_rent_review_date (date)
- auto_renewal (boolean, default: false)
- notice_period_days (integer, default: 30)
- rent_due_day
